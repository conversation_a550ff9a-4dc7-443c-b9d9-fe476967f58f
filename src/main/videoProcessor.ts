import { BrowserWindow } from 'electron';
import ffmpeg from 'fluent-ffmpeg';
import * as fs from 'fs';
import * as path from 'path';
import { MainProcessNoticeType } from '../renderer/src/types';
import ffmpegPath from '@ffmpeg-installer/ffmpeg';
import ffprobePath from '@ffprobe-installer/ffprobe';
import { SystemNotification } from './notification';

// 设置 ffmpeg 和 ffprobe 路径
ffmpeg.setFfmpegPath(ffmpegPath.path.replace('app.asar', 'app.asar.unpacked'));
ffmpeg.setFfprobePath(ffprobePath.path.replace('app.asar', 'app.asar.unpacked'));

/**
 * 视频处理工具类
 */
export class VideoProcessor {
	private processes: Map<string, any> = new Map(); // 支持多个并发进程
	private window: BrowserWindow | null = null;
	private processingQueue: string[] = []; // 处理队列
	private maxConcurrentProcesses: number = 2; // 最大并发数
	private currentProcessingCount: number = 0; // 当前处理中的文件数
	private currentProcessFunction: ((filePath: string) => Promise<any>) | null = null; // 当前处理函数

	/**
	 * 初始化视频处理器
	 * @param win Electron 窗口实例
	 */
	constructor(win: BrowserWindow) {
		this.window = win;
	}

	/**
	 * 获取视频文件信息
	 * @param filePath 视频文件路径
	 * @returns 视频文件信息
	 */
	async getVideoInfo(filePath: string) {
		try {
			// 检查文件是否存在
			if (!fs.existsSync(filePath)) {
				throw new Error('文件不存在');
			}

			// 检查文件是否为视频文件
			const ext = path.extname(filePath).toLowerCase().slice(1);
			const videoExtensions = ['mp4', 'avi', 'mkv', 'mov', 'webm', 'flv', 'ts', 'mts', 'm2ts', 'wmv', 'asf', '3gp', 'm4v'];
			if (!videoExtensions.includes(ext)) {
				throw new Error('不是支持的视频文件格式');
			}

			return new Promise((resolve, reject) => {
				ffmpeg.ffprobe(filePath, (err, metadata) => {
					if (err) {
						reject(err);
						return;
					}
					resolve(metadata);
				});
			});
		} catch (error) {
			console.error('获取视频信息失败:', error);
			throw error;
		}
	}

	/**
	 * 将视频转换为指定格式
	 * @param options 转换选项
	 * @returns 转换结果
	 */
	async convertVideoFormat(options: {
		inputPath: string;
		outputPath?: string;
		format: string;
		videoBitrate?: string;
		audioBitrate?: string;
		width?: number;
		height?: number;
		deleteOriginal?: boolean;
	}) {
		const {
			inputPath,
			format,
			videoBitrate = '1500k',
			audioBitrate = '128k',
			width,
			height,
			deleteOriginal = false
		} = options;

		// 如果没有指定输出路径，则使用输入文件的目录和名称，但更改扩展名
		let outputPath = options.outputPath;
		if (!outputPath) {
			const inputDir = path.dirname(inputPath);
			const inputFileName = path.basename(inputPath, path.extname(inputPath));
			outputPath = path.join(inputDir, `${inputFileName}.${format}`);
		}

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始转换视频格式', inputPath);

		return new Promise((resolve, reject) => {
			let command = ffmpeg(inputPath);

			// 设置输出格式
			command.format(format);

			// 设置视频比特率
			if (videoBitrate) {
				command.videoBitrate(videoBitrate);
			}

			// 设置音频比特率
			if (audioBitrate) {
				command.audioBitrate(audioBitrate);
			}

			// 设置分辨率
			if (width && height) {
				command.size(`${width}x${height}`);
			}

			// 根据格式设置编解码器
			switch (format) {
				case 'mp4':
					command.videoCodec('libx264').audioCodec('aac');
					break;
				case 'webm':
					command.videoCodec('libvpx').audioCodec('libvorbis');
					break;
				case 'avi':
					command.videoCodec('libxvid').audioCodec('libmp3lame');
					break;
				case 'mkv':
					command.videoCodec('libx264').audioCodec('aac');
					break;
				case 'mov':
					command.videoCodec('libx264').audioCodec('aac');
					break;
				case 'flv':
					command.videoCodec('flv').audioCodec('aac');
					break;
			}

			// 监听进度
			command.on('progress', (progress) => {
				this.sendNotice(MainProcessNoticeType.PROGRESS, progress.percent, inputPath);
			});

			// 处理错误
			command.on('error', (err) => {
				this.sendNotice(MainProcessNoticeType.ERROR, `视频格式转换失败: ${err.message}`, inputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;
				this.processNextInQueue();
				reject(err);
			});

			// 处理完成
			command.on('end', () => {
				this.sendNotice(MainProcessNoticeType.SUCCESS, '视频格式转换完成', outputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;

				// 如果需要删除原视频
				if (deleteOriginal && fs.existsSync(inputPath)) {
					try {
						fs.unlinkSync(inputPath);
						this.sendNotice(MainProcessNoticeType.SUCCESS, '原视频已删除', inputPath);
					} catch (error: unknown) {
						console.error('删除原视频失败:', error);
						this.sendNotice(MainProcessNoticeType.ERROR, `删除原视频失败: ${this.handleError(error)}`, inputPath);
					}
				}

				this.processNextInQueue();
				resolve(outputPath);
			});

			// 保存文件
			command.save(outputPath);

			// 保存进程引用，以便可以停止它
			this.processes.set(inputPath, command);
		});
	}

	/**
	 * 压缩视频
	 * @param options 压缩选项
	 * @returns 压缩结果
	 */
	async compressVideo(options: {
		inputPath: string;
		outputPath?: string;
		videoBitrate: string;
		audioBitrate?: string;
		width?: number;
		height?: number;
		fps?: number;
		preset?: string;
		deleteOriginal?: boolean;
	}) {
		const {
			inputPath,
			videoBitrate,
			audioBitrate = '128k',
			width,
			height,
			fps,
			preset = 'medium', // 可选值: ultrafast, superfast, veryfast, faster, fast, medium, slow, slower, veryslow
			deleteOriginal = false
		} = options;

		// 如果没有指定输出路径，则使用输入文件的目录和名称，添加后缀
		let outputPath = options.outputPath;
		if (!outputPath) {
			const inputDir = path.dirname(inputPath);
			const inputFileName = path.basename(inputPath, path.extname(inputPath));
			const ext = path.extname(inputPath);
			outputPath = path.join(inputDir, `${inputFileName}-compressed${ext}`);
		}

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始压缩视频', inputPath);

		return new Promise((resolve, reject) => {
			let command = ffmpeg(inputPath);

			// 设置视频比特率
			command.videoBitrate(videoBitrate);

			// 设置音频比特率
			if (audioBitrate) {
				command.audioBitrate(audioBitrate);
			}

			// 设置分辨率
			if (width && height) {
				command.size(`${width}x${height}`);
			}

			// 设置帧率
			if (fps) {
				command.fps(fps);
			}

			// 设置编码器预设
			command.addOption('-preset', preset);

			// 使用 H.264 编码器
			command.videoCodec('libx264');

			// 监听进度
			command.on('progress', (progress) => {
				this.sendNotice(MainProcessNoticeType.PROGRESS, progress.percent, inputPath);
			});

			// 处理错误
			command.on('error', (err) => {
				this.sendNotice(MainProcessNoticeType.ERROR, `视频压缩失败: ${err.message}`, inputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;
				this.processNextInQueue();
				reject(err);
			});

			// 处理完成
			command.on('end', () => {
				this.sendNotice(MainProcessNoticeType.SUCCESS, '视频压缩完成', outputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;

				// 如果需要删除原视频
				if (deleteOriginal && fs.existsSync(inputPath)) {
					try {
						fs.unlinkSync(inputPath);
						this.sendNotice(MainProcessNoticeType.SUCCESS, '原视频已删除', inputPath);
					} catch (error: unknown) {
						console.error('删除原视频失败:', error);
						this.sendNotice(MainProcessNoticeType.ERROR, `删除原视频失败: ${this.handleError(error)}`, inputPath);
					}
				}

				this.processNextInQueue();
				resolve(outputPath);
			});

			// 保存文件
			command.save(outputPath);

			// 保存进程引用，以便可以停止它
			this.processes.set(inputPath, command);
		});
	}

	/**
	 * 从视频中每隔指定时间截取一帧
	 * @param options 截帧选项
	 * @returns 截帧结果
	 */
	async extractFrames(options: {
		inputPath: string;
		outputDir: string;
		interval: number; // 间隔秒数
		format?: string; // 输出图片格式，默认为 jpg
		quality?: number; // 图片质量 (1-100)，默认为 80
	}) {
		const {
			inputPath,
			outputDir,
			interval,
			format = 'jpg',
			quality = 80
		} = options;

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 检查输出目录是否存在，如果不存在则创建
		if (!fs.existsSync(outputDir)) {
			fs.mkdirSync(outputDir, { recursive: true });
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始截取视频帧', inputPath);

		// 获取视频信息，以确定总时长
		const videoInfo: any = await this.getVideoInfo(inputPath);
		const duration = parseFloat(videoInfo.format.duration);

		// 计算需要截取的帧数
		const frameCount = Math.floor(duration / interval);

		// 生成输出文件名模板
		const inputFileName = path.basename(inputPath, path.extname(inputPath));
		const outputPattern = path.join(outputDir, `${inputFileName}_%03d.${format}`);

		return new Promise((resolve, reject) => {
			let command = ffmpeg(inputPath);

			// 设置截帧间隔
			command.outputOptions([
				`-vf fps=1/${interval}`,
				`-q:v ${Math.round((100 - quality) / 10)}` // 转换质量参数
			]);

			// 监听进度 (注意：ffmpeg 在截帧时可能不会报告详细进度)
			let framesDone = 0;
			command.on('progress', () => {
				framesDone++;
				const percent = Math.min(100, Math.round((framesDone / frameCount) * 100));
				this.sendNotice(MainProcessNoticeType.PROGRESS, percent, inputPath);
			});

			// 处理错误
			command.on('error', (err) => {
				this.sendNotice(MainProcessNoticeType.ERROR, `视频截帧失败: ${err.message}`, inputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;
				this.processNextInQueue();
				reject(err);
			});

			// 处理完成
			command.on('end', () => {
				this.sendNotice(MainProcessNoticeType.SUCCESS, '视频截帧完成', outputDir);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;
				this.processNextInQueue();
				resolve(outputDir);
			});

			// 保存文件
			command.save(outputPattern);

			// 保存进程引用，以便可以停止它
			this.processes.set(inputPath, command);
		});
	}

	/**
	 * 从视频中提取音频
	 * @param options 提取选项
	 * @returns 提取结果
	 */
	async extractAudio(options: {
		inputPath: string;
		outputPath?: string;
		format: 'mp3' | 'aac' | 'wav' | 'flac' | 'ogg';
		bitrate?: string;
		sampleRate?: string;
		channels?: number;
	}) {
		const {
			inputPath,
			format,
			bitrate = '192k',
			sampleRate = '44100',
			channels = 2
		} = options;

		// 如果没有指定输出路径，则使用输入文件的目录和名称，但更改扩展名
		let outputPath = options.outputPath;
		if (!outputPath) {
			const inputDir = path.dirname(inputPath);
			const inputFileName = path.basename(inputPath, path.extname(inputPath));
			outputPath = path.join(inputDir, `${inputFileName}.${format}`);
		}

		// 检查输入文件是否存在
		if (!fs.existsSync(inputPath)) {
			this.sendNotice(MainProcessNoticeType.ERROR, '输入文件不存在', inputPath);
			throw new Error('输入文件不存在');
		}

		// 发送开始通知
		this.sendNotice(MainProcessNoticeType.START, '开始提取音频', inputPath);

		return new Promise((resolve, reject) => {
			let command = ffmpeg(inputPath);

			// 移除视频流
			command.noVideo();

			// 设置音频编码器和参数
			switch (format) {
				case 'mp3':
					command.audioCodec('libmp3lame').audioBitrate(bitrate);
					break;
				case 'aac':
					command.audioCodec('aac').audioBitrate(bitrate);
					break;
				case 'wav':
					command.audioCodec('pcm_s16le');
					break;
				case 'flac':
					command.audioCodec('flac');
					break;
				case 'ogg':
					command.audioCodec('libvorbis').audioBitrate(bitrate);
					break;
			}

			// 设置采样率和声道
			command.audioFrequency(parseInt(sampleRate));
			command.audioChannels(channels);

			// 监听进度
			command.on('progress', (progress) => {
				this.sendNotice(MainProcessNoticeType.PROGRESS, progress.percent, inputPath);
			});

			// 处理错误
			command.on('error', (err) => {
				this.sendNotice(MainProcessNoticeType.ERROR, `音频提取失败: ${err.message}`, inputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;
				this.processNextInQueue();
				reject(err);
			});

			// 处理完成
			command.on('end', () => {
				this.sendNotice(MainProcessNoticeType.SUCCESS, '音频提取完成', outputPath);
				this.processes.delete(inputPath);
				this.currentProcessingCount--;
				this.processNextInQueue();
				resolve(outputPath);
			});

			// 保存文件
			command.save(outputPath);

			// 保存进程引用，以便可以停止它
			this.processes.set(inputPath, command);
		});
	}

	/**
	 * 停止所有处理进程
	 */
	stop() {
		// 清空队列
		this.processingQueue = [];
		this.currentProcessFunction = null;

		// 停止所有正在运行的进程
		for (const [filePath, process] of this.processes) {
			try {
				process.kill('SIGKILL');
				this.sendNotice(MainProcessNoticeType.STOP, '处理已停止', filePath);
			} catch (error) {
				console.error('停止处理时出错:', error);
			}
		}

		// 清空进程映射
		this.processes.clear();
		this.currentProcessingCount = 0;
	}

	/**
	 * 添加文件到处理队列
	 * @param filePaths 文件路径数组
	 * @param processFunction 处理函数
	 */
	addToQueue(filePaths: string[], processFunction: (filePath: string) => Promise<any>) {
		// 保存处理函数
		this.currentProcessFunction = processFunction;

		// 添加到队列
		this.processingQueue.push(...filePaths);

		// 开始处理队列中的文件
		this.processQueue();
	}

	/**
	 * 处理队列中的文件
	 */
	private processQueue() {
		// 处理多个文件，直到达到最大并发数或队列为空
		while (this.currentProcessingCount < this.maxConcurrentProcesses && this.processingQueue.length > 0) {
			const filePath = this.processingQueue.shift();
			if (filePath && this.currentProcessFunction) {
				this.currentProcessingCount++;
				this.currentProcessFunction(filePath).catch((error) => {
					console.error('处理文件失败:', error);
					this.currentProcessingCount--;
					this.processQueue(); // 继续处理队列中的下一个文件
				});
			}
		}
	}

	/**
	 * 处理队列中的下一个文件
	 */
	private processNextInQueue() {
		// 继续处理队列中的文件
		this.processQueue();
	}

	/**
	 * 批量压缩视频
	 * @param filePaths 文件路径数组
	 * @param options 压缩选项
	 */
	async batchCompressVideo(filePaths: string[], options: {
		videoBitrate: string;
		audioBitrate?: string;
		width?: number;
		height?: number;
		fps?: number;
		preset?: string;
		deleteOriginal?: boolean;
		saveDirectory?: string;
	}) {
		// 清空之前的队列
		this.processingQueue = [];

		// 创建处理函数
		const processFunction = async (filePath: string) => {
			// 构建输出路径
			let outputPath = '';
			if (options.saveDirectory) {
				const fileName = path.basename(filePath, path.extname(filePath));
				const ext = path.extname(filePath);
				outputPath = path.join(options.saveDirectory, `${fileName}-compressed${ext}`);
			}

			return this.compressVideo({
				inputPath: filePath,
				outputPath,
				...options
			});
		};

		// 添加到队列并开始处理
		this.addToQueue(filePaths, processFunction);
	}

	/**
	 * 发送通知到渲染进程
	 * @param type 通知类型
	 * @param data 通知数据
	 * @param path 文件路径
	 */
	private sendNotice(type: MainProcessNoticeType, data: any, path: string) {
		if (this.window && !this.window.isDestroyed()) {
			this.window.webContents.send('mainProcessNotice', type, data, path);

			// 对于成功完成的操作，发送系统通知
			if (type === MainProcessNoticeType.SUCCESS) {
				const fileName = path ? this.getFileName(path) : '';
				SystemNotification.show('操作完成', `${data} ${fileName}`);
			}
		}
	}

	/**
	 * 获取文件名
	 * @param filePath 文件路径
	 * @returns 文件名
	 */
	private getFileName(filePath: string): string {
		if (!filePath) return '';
		// 处理不同操作系统的路径分隔符
		const normalizedPath = filePath.replace(/\\/g, '/');
		return normalizedPath.split('/').pop() || filePath;
	}

	/**
	 * 处理错误对象
	 * @param error 错误对象
	 * @returns 错误消息
	 */
	private handleError(error: unknown): string {
		if (error instanceof Error) {
			return error.message;
		}
		return String(error);
	}
}
