import { BrowserWindow, IpcMainInvokeEvent, ipcMain, shell } from 'electron'
import Ffmpeg from './ffmpeg'
import { selectDirectory, selectFile } from './directory'
import {
	CompressOptions,
	ExtractAudioOptions,
	VideoConvertOptions,
	VideoCompressOptions,
	ExtractFramesOptions,
	ImageCompressOptions,
	ImageConvertOptions,
	ImageEnhanceOptions
} from '../renderer/src/types'
import { ts2mp4 } from './ts2mp4'
import { simulateVideoInfo } from './videoInfo'
import AudioExtractor from './extractAudio'
import path from 'path'
import { VideoProcessor } from './videoProcessor'
import { ImageProcessor } from './imageProcessor'

function registerIPC(win: BrowserWindow) {
	//压缩视频
	const ffmpeg = new Ffmpeg()
	const audioExtractor = new AudioExtractor()
	const videoProcessor = new VideoProcessor(win)
	const imageProcessor = new ImageProcessor(win)

	// 旧版压缩视频
	ipcMain.handle('compress', async (_event: IpcMainInvokeEvent, options: CompressOptions) => {
		ffmpeg.init(win, options).run()
	})

	// 提取音频
	ipcMain.handle('extractAudio', async (_event: IpcMainInvokeEvent, options: ExtractAudioOptions) => {
		return videoProcessor.extractAudio(options)
	})

	ipcMain.on('stop', () => {
		ffmpeg.stop()
		audioExtractor.stop()
		videoProcessor.stop()
		imageProcessor.stop()
	})

	ipcMain.handle('selectDirectory', async () => {
		return selectDirectory()
	})

	ipcMain.handle('ts2mp4', async () => {
		return ts2mp4()
	})

	ipcMain.handle('selectFile', async (_event: IpcMainInvokeEvent, fileType: 'video' | 'image' | 'all' = 'all', multiple: boolean = true) => {
		return selectFile(win, fileType, multiple)
	})

	// 打开文件所在目录
	ipcMain.handle('openFileDirectory', async (_event: IpcMainInvokeEvent, filePath: string) => {
		if (filePath) {
			shell.showItemInFolder(filePath)
			return true
		}
		return false
	})

	// 获取视频信息
	ipcMain.handle('getVideoInfo', async (_event: IpcMainInvokeEvent, filePath: string) => {
		if (!filePath) {
			throw new Error('文件路径不能为空');
		}

		try {
			return await videoProcessor.getVideoInfo(filePath);
		} catch (error) {
			console.error('获取视频信息失败:', error);
			// 如果实际获取失败，回退到模拟数据
			return simulateVideoInfo(filePath);
		}
	})

	// 视频格式转换
	ipcMain.handle('convertVideoFormat', async (_event: IpcMainInvokeEvent, options: VideoConvertOptions) => {
		return videoProcessor.convertVideoFormat(options)
	})

	// 视频压缩（新版）
	ipcMain.handle('compressVideo', async (_event: IpcMainInvokeEvent, options: VideoCompressOptions) => {
		return videoProcessor.compressVideo(options)
	})

	// 批量视频压缩
	ipcMain.handle('batchCompressVideo', async (_event: IpcMainInvokeEvent, filePaths: string[], options: Omit<VideoCompressOptions, 'inputPath'>) => {
		return videoProcessor.batchCompressVideo(filePaths, options)
	})

	// 视频截帧
	ipcMain.handle('extractFrames', async (_event: IpcMainInvokeEvent, options: ExtractFramesOptions) => {
		return videoProcessor.extractFrames(options)
	})

	// 图片压缩
	ipcMain.handle('compressImage', async (_event: IpcMainInvokeEvent, options: ImageCompressOptions) => {
		return imageProcessor.compressImage(options)
	})

	// 图片格式转换
	ipcMain.handle('convertImageFormat', async (_event: IpcMainInvokeEvent, options: ImageConvertOptions) => {
		return imageProcessor.convertImageFormat(options)
	})

	// 图片清晰度提升
	ipcMain.handle('enhanceImage', async (_event: IpcMainInvokeEvent, options: ImageEnhanceOptions) => {
		return imageProcessor.enhanceImage(options)
	})

	// 处理文件拖放
	ipcMain.handle('handle-drag-drop-files', async (_event: IpcMainInvokeEvent, fileList: any[]) => {
		try {
			// 从渲染进程接收到的文件对象中提取文件路径
			// 在主进程中，我们可以安全地访问文件系统
			const filePaths = fileList.map(file => {
				// 如果文件对象有 path 属性，直接使用
				if (file.path) {
					return file.path;
				}

				// 如果没有 path 属性但有其他可用信息，尝试构建路径
				// 这取决于操作系统和 Electron 版本
				if (file.name) {
					// 这里只是一个示例，实际情况可能需要更复杂的处理
					return path.join(file.name);
				}

				return null;
			}).filter(Boolean); // 过滤掉无效的路径

			return filePaths;
		} catch (error) {
			console.error('处理拖放文件出错:', error);
			return [];
		}
	});

	// 旧的拖拽处理方法，已被新的 webUtils.getPathForFile 替代
	// ipcMain.on('handle-file-entries', async (event, entries) => {
	// 	console.log("🚀 ~ ipcMain.on ~ entries:", entries)
	// 	// 在主进程中处理文件路径
	// 	const filePaths = entries
	// 		.filter(entry => entry.isFile)
	// 		.map(entry => {
	// 			// 在主进程中，我们可以安全地访问文件系统
	// 			// 这里可以使用 app.getPath() 或其他 Electron API 来获取完整路径
	// 			return path.resolve(entry.fullPath);
	// 		});

	// 	event.reply('files-path-ready', filePaths);
	// });

}
export default registerIPC
