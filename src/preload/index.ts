import { electronAPI } from '@electron-toolkit/preload'

// 使用标准的 electronAPI，文件路径获取统一通过 api.getPathForFile 处理
const extendedElectronAPI = electronAPI
import { IpcRendererEvent, contextBridge, ipcRenderer, webUtils } from 'electron'
import {
  CompressOptions,
  ExtractAudioOptions,
  MainProcessNoticeType,
  VideoConvertOptions,
  VideoCompressOptions,
  BatchVideoCompressOptions,
  ExtractFramesOptions,
  ImageCompressOptions,
  ImageConvertOptions,
  ImageEnhanceOptions
} from '../renderer/src/types'

// Custom APIs for renderer
const api = {
  // 旧版压缩视频
  compress: (options: CompressOptions) => {
    ipcRenderer.invoke('compress', options)
  },
  // 提取音频
  extractAudio: (options: ExtractAudioOptions) => {
    return ipcRenderer.invoke('extractAudio', options)
  },
  //选择文件
  selectFile: (fileType: 'video' | 'image' | 'all' = 'all', multiple: boolean = true) => {
    return ipcRenderer.invoke('selectFile', fileType, multiple)
  },
  //选择目录
  selectDirectory: () => {
    return ipcRenderer.invoke('selectDirectory')
  },
  ts2mp4: () => {
    return ipcRenderer.invoke('ts2mp4')
  },
  // 获取视频信息
  getVideoInfo: (filePath: string) => {
    return ipcRenderer.invoke('getVideoInfo', filePath)
  },
  // 打开文件所在目录
  openFileDirectory: (filePath: string) => {
    return ipcRenderer.invoke('openFileDirectory', filePath)
  },
  // 处理拖放文件，通过主进程获取文件路径
  handleDragDropFiles: (fileList: any[]) => {
    return ipcRenderer.invoke('handle-drag-drop-files', fileList)
  },
  // 获取文件路径（统一处理，内部实现回退机制）
  getPathForFile: (file: File) => {
    try {
      // 优先使用 webUtils.getPathForFile
      return webUtils.getPathForFile(file)
    } catch (error) {
      console.warn('webUtils.getPathForFile 失败，尝试备用方案:', error)
      // 这里可以添加其他备用方案，目前 webUtils.getPathForFile 是最可靠的
      throw new Error('无法获取文件路径')
    }
  },
  // 停止所有处理
  stop() {
    ipcRenderer.send('stop')
  },

  // 新增功能 - 视频处理
  // 视频格式转换
  convertVideoFormat: (options: VideoConvertOptions) => {
    return ipcRenderer.invoke('convertVideoFormat', options)
  },
  // 视频压缩（新版）
  compressVideo: (options: VideoCompressOptions) => {
    return ipcRenderer.invoke('compressVideo', options)
  },
  // 批量视频压缩
  batchCompressVideo: (filePaths: string[], options: BatchVideoCompressOptions) => {
    return ipcRenderer.invoke('batchCompressVideo', filePaths, options)
  },
  // 视频截帧
  extractFrames: (options: ExtractFramesOptions) => {
    return ipcRenderer.invoke('extractFrames', options)
  },

  // 新增功能 - 图片处理
  // 图片压缩
  compressImage: (options: ImageCompressOptions) => {
    return ipcRenderer.invoke('compressImage', options)
  },
  // 图片格式转换
  convertImageFormat: (options: ImageConvertOptions) => {
    return ipcRenderer.invoke('convertImageFormat', options)
  },
  // 图片清晰度提升
  enhanceImage: (options: ImageEnhanceOptions) => {
    return ipcRenderer.invoke('enhanceImage', options)
  },

  // 进程通知
  mainProcessNotice: (callback: (type: MainProcessNoticeType, data: any, path: string) => void) => {
    const listener = (_event: IpcRendererEvent, type: MainProcessNoticeType, data: any, path: string) => {
      callback(type, data, path)
    }

    ipcRenderer.on('mainProcessNotice', listener)

    // 返回一个取消订阅的函数
    return () => {
      ipcRenderer.removeListener('mainProcessNotice', listener)
    }
  },

  // 旧的拖拽处理方法，现在已被各个组件中的新实现替代
  handleFiles: (callback: (filePaths: string[]) => void) => {
    console.warn('handleFiles 方法已被弃用，请使用组件级别的拖拽处理');
    // 不再添加全局事件监听器，也不再发送 IPC 消息
    // callback 参数保留以保持向后兼容性
    void callback;
  }
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
console.log("🚀 ~ process.contextIsolated:", process.contextIsolated)
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', extendedElectronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = extendedElectronAPI
  // @ts-ignore (define in dts)
  window.api = api
}

console.log("🚀 ~ window:", window)

// 拦截全局拖拽，防止页面默认打开文件
window.addEventListener('dragover', e => e.preventDefault());
// TODO:
// window.addEventListener('drop', e => {
//   console.log("🚀 ~ files ~ e:", e)
//   console.log("🚀 ~ files ~ e.dataTransfer.files:", e.dataTransfer.files)
//   e.preventDefault();

//   // 这里拿到的每个 File 对象都含 .path
//   const files = Array.from(e.dataTransfer.files).map(f => ({
//     path: f.path,     // 真正的本地路径，只在主进程用
//     name: f.name,
//     type: f.type,
//     size: f.size
//   }));
//   console.log("🚀 ~ files ~ files:", files)

// });
