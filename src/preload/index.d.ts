import { ElectronAPI } from '@electron-toolkit/preload'
import {
	CompressOptions,
	ExtractAudioOptions,
	MainProcessNoticeType,
	VideoConvertOptions,
	VideoCompressOptions,
	ExtractFramesOptions,
	ImageCompressOptions,
	ImageConvertOptions,
	ImageEnhanceOptions
} from '@renderer/types'

declare global {
	interface Window {
		electron: ElectronAPI
		api: {
			// 旧版功能
			compress: (options: CompressOptions) => void
			extractAudio: (options: ExtractAudioOptions) => Promise<string>
			selectDirectory: () => Promise<any>
			ts2mp4: () => Promise<any>
			selectFile: (fileType?: 'video' | 'image' | 'all', multiple?: boolean) => Promise<any>
			getVideoInfo: (filePath: string) => Promise<any>
			openFileDirectory: (filePath: string) => Promise<boolean>
			handleDragDropFiles: (fileList: any[]) => Promise<string[]>
			getPathForFile: (file: File) => string
			stop: () => void

			// 视频处理新功能
			convertVideoFormat: (options: VideoConvertOptions) => Promise<string>
			compressVideo: (options: VideoCompressOptions) => Promise<string>
			batchCompressVideo: (filePaths: string[], options: Omit<VideoCompressOptions, 'inputPath'>) => Promise<void>
			extractFrames: (options: ExtractFramesOptions) => Promise<string>

			// 图片处理新功能
			compressImage: (options: ImageCompressOptions) => Promise<string>
			convertImageFormat: (options: ImageConvertOptions) => Promise<string>
			enhanceImage: (options: ImageEnhanceOptions) => Promise<string>
			handleFiles: (callback: (filePaths: string[]) => void) => void

			// 进程通知
			mainProcessNotice: (
				callback: (type: MainProcessNoticeType, data: any, path: string) => void
			) => () => void
		}
	}
}
