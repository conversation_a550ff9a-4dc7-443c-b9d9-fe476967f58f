import { createFileRoute } from '@tanstack/react-router'
import { useState, useEffect } from 'react'
import { VideoCompressForm } from '@renderer/components/video/VideoCompressForm'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MainProcessNoticeType } from '@renderer/types'
import { toast } from 'sonner'
import path from 'path-browserify-esm'
import { FileUploadList } from '@renderer/components/common/FileUploadList'
import { useVideoStore } from '@renderer/store/videoStore'

export const Route = createFileRoute('/video/compress')({
	component: VideoCompress
})

function VideoCompress() {
	// 使用 Zustand store 管理文件状态
	const {
		files: storeFiles,
		currentFile,
		isProcessing,
		setCurrentFile,
		setProcessing,
		setFileStatus
	} = useVideoStore()

	// 将 store 中的文件对象转换为路径数组，以兼容现有组件
	const files = storeFiles.map(file => file.path)

	// 构建文件状态对象，用于传递给 FileList 组件
	const fileProgress: Record<string, number> = {}
	storeFiles.forEach(file => {
		if (file.status !== undefined && typeof file.status === 'number') {
			fileProgress[file.path] = file.status
		}
	})

	const [compressSettings, setCompressSettings] = useState({
		resolution: '720p',
		fps: 30,
		bitrate: 1000,
		saveDirectory: '',
		preset: 'medium',
		deleteOriginal: false
	})
	const [progress, setProgress] = useState(0)

	// 监听主进程通知
	useEffect(() => {
		const unsubscribe = window.api.mainProcessNotice((type, data, filePath) => {
			switch (type) {
				case MainProcessNoticeType.START:
					setCurrentFile(filePath)
					setFileStatus(filePath, 0)
					toast.info('开始压缩视频', { description: path.basename(filePath) })
					break
				case MainProcessNoticeType.PROGRESS:
					setFileStatus(filePath, data)
					// 计算整体进度
					const totalFiles = files.length
					const completedFiles = storeFiles.filter(f => f.status === 100).length
					const currentProgress = data || 0
					const overallProgress = totalFiles > 0 ?
						Math.round((completedFiles * 100 + currentProgress) / totalFiles) : 0
					setProgress(overallProgress)
					break
				case MainProcessNoticeType.SUCCESS:
					toast.success('视频压缩成功', { description: path.basename(filePath) })
					setFileStatus(filePath, 100)

					// 检查是否所有文件都完成了
					const allCompleted = storeFiles.every(f =>
						f.path === filePath || f.status === 100 || f.status === -1
					)

					if (allCompleted) {
						setProcessing(false)
						setCurrentFile(null)
						setProgress(100)
						const successCount = storeFiles.filter(f => f.status === 100).length + 1
						toast.success('所有视频压缩完成', {
							description: `成功处理 ${successCount} 个文件`
						})
					}
					break
				case MainProcessNoticeType.ERROR:
					toast.error('视频压缩失败', { description: data })
					setFileStatus(filePath, -1) // -1 表示错误

					// 检查是否所有文件都完成了（包括错误的）
					const allDone = storeFiles.every(f =>
						f.path === filePath || f.status === 100 || f.status === -1
					)

					if (allDone) {
						setProcessing(false)
						setCurrentFile(null)
						const successCount = storeFiles.filter(f => f.status === 100).length
						const errorCount = storeFiles.filter(f => f.status === -1).length + 1
						toast.info('批量压缩完成', {
							description: `成功 ${successCount} 个，失败 ${errorCount} 个`
						})
					}
					break
				case MainProcessNoticeType.STOP:
					toast.info('视频压缩已停止')
					setProcessing(false)
					setCurrentFile(null)
					setProgress(0)
					break
			}
		})

		return () => {
			unsubscribe()
		}
	}, [files, storeFiles, setCurrentFile, setProcessing, setFileStatus])



	// 获取分辨率的宽高
	const getResolutionDimensions = (resolution: string) => {
		const resolutions: Record<string, { width: number, height: number }> = {
			'480p': { width: 854, height: 480 },
			'720p': { width: 1280, height: 720 },
			'1080p': { width: 1920, height: 1080 },
			'1440p': { width: 2560, height: 1440 },
			'2160p': { width: 3840, height: 2160 }
		}
		return resolutions[resolution] || { width: 1280, height: 720 }
	}



	const handleCompress = async () => {
		if (files.length === 0) {
			toast.error('请先选择视频文件')
			return
		}

		try {
			setProcessing(true)
			setProgress(0)

			// 重置所有文件状态
			files.forEach(file => setFileStatus(file, 0))

			// 获取分辨率的宽高
			const { width, height } = getResolutionDimensions(compressSettings.resolution)

			// 调用批量压缩函数
			await window.api.batchCompressVideo(files, {
				videoBitrate: `${compressSettings.bitrate}k`,
				width,
				height,
				fps: compressSettings.fps,
				preset: compressSettings.preset,
				deleteOriginal: compressSettings.deleteOriginal,
				saveDirectory: compressSettings.saveDirectory
			})
		} catch (error) {
			console.error('Error starting batch compression:', error)
			toast.error('启动批量压缩失败', { description: String(error) })
			setProcessing(false)
			setProgress(0)
		}
	}

	const handleStop = () => {
		window.api.stop()
		setProcessing(false)
		setCurrentFile(null)
		setProgress(0)
	}

	const handleSettingsChange = (settings: Partial<typeof compressSettings>) => {
		setCompressSettings({ ...compressSettings, ...settings })
	}

	const handleSelectDirectory = async () => {
		try {
			const directory = await window.api.selectDirectory()
			if (directory) {
				setCompressSettings({ ...compressSettings, saveDirectory: directory })
			}
		} catch (error) {
			console.error('Error selecting directory:', error)
		}
	}

	return (
		<div className="flex flex-col min-h-0 h-full">
			{/* 页面标题 */}
			<div className="flex-none">
				<h2 className="text-[#262626] text-xl font-medium">视频压缩</h2>
				<p className="text-[#8C8C8C] text-sm mt-1">压缩视频文件大小，支持调整分辨率、帧率等参数</p>
			</div>

			<div className="flex-1 mt-6 min-h-0">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-0 rounded-lg overflow-hidden bg-white h-full">
					{/* 文件列表区 */}
					<div className="md:col-span-2 border-r border-slate-200 overflow-auto">
						<FileUploadList
							storeType="video"
						/>
					</div>

					{/* 压缩设置区 */}
					<div className="md:col-span-1 overflow-auto">
						<Card className="h-full">
							<CardHeader>
								<CardTitle className="text-base font-medium">压缩设置</CardTitle>
							</CardHeader>
							<CardContent>
								<VideoCompressForm
									settings={compressSettings}
									onSettingsChange={handleSettingsChange}
									onSelectDirectory={handleSelectDirectory}
									onCompress={handleCompress}
									onStop={handleStop}
									disabled={files.length === 0 || isProcessing}
									isProcessing={isProcessing}
									progress={progress}
								/>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	)
}
